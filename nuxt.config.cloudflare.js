// Cloudflare Pages specific Nuxt configuration
// This config excludes dev-only modules that may cause build issues

// Google Analytics configuration - Environment-aware setup
const gaId = process.env.NUXT_ENV_GA_ID || 'G-PN1XZ4X9VG'
const baseUrl = process.env.NUXT_ENV_BASE_URL || 'https://yugiohcardmaker.org'

const gaTags = [
  {
    hid: 'gtag-script',
    src: `https://www.googletagmanager.com/gtag/js?id=${gaId}`,
    async: true
  },
  {
    hid: 'gtag-config',
    innerHTML: `
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());
      gtag('config', '${gaId}', {
        page_title: document.title,
        page_location: window.location.href,
        custom_map: {'custom_parameter': 'yugioh_card_maker'}
      });
    `,
    type: 'text/javascript',
    charset: 'utf-8'
  }
]

export default {
  // Disable server-side rendering: https://go.nuxtjs.dev/ssr-mode
  ssr: false,

  // Target: https://go.nuxtjs.dev/config-target
  target: 'static',

  // Environment-specific configuration
  publicRuntimeConfig: {
    baseURL: process.env.NUXT_ENV_BASE_URL || 'https://yugiohcardmaker.org',
    gaId: process.env.NUXT_ENV_GA_ID || 'G-PN1XZ4X9VG',
    cdnUrl: process.env.NUXT_ENV_CDN_URL || 'https://yugiohcardmaker.org'
  },

  privateRuntimeConfig: {
    // Private keys which are only available on server-side
  },

  // Global page headers: https://go.nuxtjs.dev/config-head
  head: {
    title: 'Yu-Gi-Oh! Card Maker',
    titleTemplate: '%s',
    htmlAttrs: {
      lang: 'en'
    },
    meta: [
      // Basic meta tags
      { charset: 'utf-8' },
      { name: 'viewport', content: 'width=device-width, initial-scale=1, shrink-to-fit=no' },
      { name: 'format-detection', content: 'telephone=no' },
      { name: 'theme-color', content: '#2c3e50' },

      // Google AdSense meta tag
      { name: 'google-adsense-account', content: 'ca-pub-****************' },

      // Basic SEO meta tags
      { hid: 'author', name: 'author', content: 'yugiohcardmaker.org' },
      { hid: 'robots', name: 'robots', content: 'index, follow' },
      { hid: 'googlebot', name: 'googlebot', content: 'index, follow' },

      // Open Graph meta tags (og:url will be set dynamically by pages)
      { hid: 'og:type', property: 'og:type', content: 'website' },
      { hid: 'og:site_name', property: 'og:site_name', content: 'Yu-Gi-Oh! Card Maker' },
      { hid: 'og:image', property: 'og:image', content: `${baseUrl}/images/og-image.jpg` },
      { hid: 'og:image:width', property: 'og:image:width', content: '1200' },
      { hid: 'og:image:height', property: 'og:image:height', content: '630' },

      // Twitter Card meta tags
      { hid: 'twitter:card', name: 'twitter:card', content: 'summary_large_image' },
      { hid: 'twitter:site', name: 'twitter:site', content: '@yugiohcardmaker' },
      { hid: 'twitter:creator', name: 'twitter:creator', content: '@yugiohcardmaker' },
      { hid: 'twitter:image', name: 'twitter:image', content: `${baseUrl}/images/twitter-card.jpg` },

      // PWA meta tags
      { hid: 'application-name', name: 'application-name', content: 'Yu-Gi-Oh! Card Maker' },
      { hid: 'apple-mobile-web-app-title', name: 'apple-mobile-web-app-title', content: 'Yu-Gi-Oh! Card Maker' },
      { hid: 'apple-mobile-web-app-capable', name: 'apple-mobile-web-app-capable', content: 'yes' },
      { hid: 'apple-mobile-web-app-status-bar-style', name: 'apple-mobile-web-app-status-bar-style', content: 'black-translucent' },
      { hid: 'msapplication-TileColor', name: 'msapplication-TileColor', content: '#2c3e50' },
      { hid: 'msapplication-config', name: 'msapplication-config', content: '/browserconfig.xml' }
    ],
    link: [
      // Favicons
      { rel: 'icon', type: 'image/x-icon', href: '/favicon.ico' },
      { rel: 'icon', type: 'image/png', sizes: '32x32', href: '/favicon-32x32.png' },
      { rel: 'icon', type: 'image/png', sizes: '16x16', href: '/favicon-16x16.png' },
      { rel: 'apple-touch-icon', sizes: '180x180', href: '/apple-touch-icon.png' },
      { rel: 'manifest', href: '/site.webmanifest' },

      // Preconnect to external domains
      { rel: 'preconnect', href: 'https://fonts.googleapis.com' },
      { rel: 'preconnect', href: 'https://fonts.gstatic.com', crossorigin: true }
    ],
    script: [
      ...gaTags,
      // Google AdSense script
      {
        hid: 'google-adsense',
        src: 'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-****************',
        async: true,
        crossorigin: 'anonymous'
      },
      // Structured data for SEO
      {
        hid: 'structured-data',
        type: 'application/ld+json',
        innerHTML: JSON.stringify({
          '@context': 'https://schema.org',
          '@type': 'WebApplication',
          name: 'Yu-Gi-Oh! Card Maker',
          alternateName: 'Yu-Gi-Oh! Card Maker',
          description: 'Free online Yu-Gi-Oh! card maker. Create custom monster, spell, and trap cards with professional quality.',
          url: `${baseUrl}/`,
          applicationCategory: 'DesignApplication',
          operatingSystem: 'Web Browser',
          offers: {
            '@type': 'Offer',
            price: '0',
            priceCurrency: 'USD'
          },
          author: {
            '@type': 'Organization',
            name: 'yugiohcardmaker.org',
            url: baseUrl
          },
          inLanguage: ['en', 'zh', 'ja', 'de', 'fr', 'ko', 'pt', 'es', 'el', 'th', 'ru', 'vi'],
          isAccessibleForFree: true,
          screenshot: `${baseUrl}/images/screenshot.jpg`
        })
      }
    ],
    __dangerouslyDisableSanitizersByTagID: {
      'gtag-config': ['innerHTML'],
      'structured-data': ['innerHTML']
    }
  },

  // Global CSS: https://go.nuxtjs.dev/config-css
  css: [
    'bootstrap-vue/dist/bootstrap-vue.css'
  ],

  // Plugins to run before rendering page: https://go.nuxtjs.dev/config-plugins
  plugins: [
    {
      src: '~/plugins/font-awesome'
    },
    {
      src: '~/plugins/gtag',
      mode: 'client'
    },
    {
      src: '~/plugins/seo.js',
      mode: 'client'
    }
  ],

  // Router configuration
  router: {
    middleware: ['seo']
  },

  // Auto import components: https://go.nuxtjs.dev/config-components
  components: true,

  // Modules for dev and build (recommended): https://go.nuxtjs.dev/config-modules
  buildModules: [
    // Remove ESLint module for Cloudflare builds
    'nuxt-font-loader'
  ],

  // Modules: https://go.nuxtjs.dev/config-modules
  modules: [
    // https://go.nuxtjs.dev/bootstrap
    'bootstrap-vue/nuxt',
    // https://go.nuxtjs.dev/axios
    '@nuxtjs/axios',
    // Internationalization
    '@nuxtjs/i18n'
  ],

  // i18n configuration
  i18n: {
    locales: [
      {
        code: 'en',
        iso: 'en-US',
        name: 'English',
        file: 'en.js'
      },
      {
        code: 'zh',
        iso: 'zh-TW',
        name: '中文',
        file: 'zh.js'
      },
      {
        code: 'ja',
        iso: 'ja-JP',
        name: '日本語',
        file: 'ja.js'
      },
      {
        code: 'de',
        iso: 'de-DE',
        name: 'Deutsch',
        file: 'de.js'
      },
      {
        code: 'fr',
        iso: 'fr-FR',
        name: 'Français',
        file: 'fr.js'
      },
      {
        code: 'ko',
        iso: 'ko-KR',
        name: '한국어',
        file: 'ko.js'
      },
      {
        code: 'pt',
        iso: 'pt-PT',
        name: 'Português',
        file: 'pt.js'
      },
      {
        code: 'es',
        iso: 'es-ES',
        name: 'Español',
        file: 'es.js'
      },
      {
        code: 'el',
        iso: 'el-GR',
        name: 'Ελληνικά',
        file: 'el.js'
      },
      {
        code: 'th',
        iso: 'th-TH',
        name: 'ไทย',
        file: 'th.js'
      },
      {
        code: 'ru',
        iso: 'ru-RU',
        name: 'Русский',
        file: 'ru.js'
      },
      {
        code: 'vi',
        iso: 'vi-VN',
        name: 'Tiếng Việt',
        file: 'vi.js'
      }
    ],
    defaultLocale: 'en',
    strategy: 'prefix_except_default',
    langDir: 'locales/',
    lazy: true,
    detectBrowserLanguage: {
      useCookie: true,
      cookieKey: 'i18n_redirected',
      redirectOn: 'root',
      alwaysRedirect: false,
      fallbackLocale: 'en'
    },
    seo: true,
    baseUrl: baseUrl,
    vueI18n: {
      fallbackLocale: 'en'
    }
  },

  // i18n configuration
  i18n: {
    locales: [
      { code: 'en', name: 'English', file: 'en.js' },
      { code: 'zh', name: '中文', file: 'zh.js' },
      { code: 'ja', name: '日本語', file: 'ja.js' },
      { code: 'de', name: 'Deutsch', file: 'de.js' },
      { code: 'fr', name: 'Français', file: 'fr.js' },
      { code: 'ko', name: '한국어', file: 'ko.js' },
      { code: 'pt', name: 'Português', file: 'pt.js' },
      { code: 'es', name: 'Español', file: 'es.js' },
      { code: 'el', name: 'Ελληνικά', file: 'el.js' },
      { code: 'th', name: 'ไทย', file: 'th.js' },
      { code: 'ru', name: 'Русский', file: 'ru.js' },
      { code: 'vi', name: 'Tiếng Việt', file: 'vi.js' }
    ],
    lazy: true,
    langDir: 'locales/',
    defaultLocale: 'en',
    strategy: 'prefix_except_default'
  },

  // Axios module configuration: https://go.nuxtjs.dev/config-axios
  axios: {
    // Workaround to avoid enforcing hard-coded localhost:3000: https://github.com/nuxt-community/axios-module/issues/308
    baseURL: '/'
  },

  // Build Configuration: https://go.nuxtjs.dev/config-build
  build: {
    sourceMap: false,
    extractCSS: true,
    optimization: {
      splitChunks: {
        chunks: 'all',
        automaticNameDelimiter: '.',
        name: undefined,
        cacheGroups: {}
      }
    }
  },

  // Generate configuration
  generate: {
    fallback: true,
    routes: [
      '/',
      '/privacy',
      '/terms',
      '/analytics-test'
    ]
  },

  // Generate hooks for SEO optimization
  hooks: {
    'generate:page': (page) => {
      const { route, html } = page
      const languages = ['en', 'zh', 'ja', 'de', 'fr', 'ko', 'pt', 'es', 'el', 'th', 'ru', 'vi']

      // Determine current locale from route
      let currentLocale = 'en'
      let cleanPath = route

      for (const lang of languages) {
        if (route.startsWith(`/${lang}/`) || route === `/${lang}`) {
          currentLocale = lang
          cleanPath = route.replace(`/${lang}`, '') || '/'
          break
        }
      }

      // Generate page title based on locale
      const titles = {
        en: 'Best Yu-Gi-Oh! Card Maker - Free Online Card Maker',
        zh: '最佳遊戲王卡片製造機 - 免費在綫設計工具',
        ja: 'ベスト遊戯王カードメーカー - 無料オンライン作成ツール',
        de: 'Bester Yu-Gi-Oh! Kartenmacher - Kostenloser Online-Kartenmacher',
        fr: 'Meilleur Créateur de Cartes Yu-Gi-Oh! - Créateur de Cartes Gratuit en Ligne',
        ko: '최고의 유희왕 카드 메이커 - 무료 온라인 카드 제작기',
        pt: 'Melhor Criador de Cartas Yu-Gi-Oh! - Criador de Cartas Gratuito Online',
        es: 'Mejor Generador de Cartas Yu-Gi-Oh! - Creador de Cartas Gratuito en Línea',
        el: 'Καλύτερος Δημιουργός Καρτών Yu-Gi-Oh! - Δωρεάν Online Δημιουργός Καρτών',
        th: 'เครื่องมือสร้างการ์ด Yu-Gi-Oh! ที่ดีที่สุด - เครื่องมือสร้างการ์ดออนไลน์ฟรี',
        ru: 'Лучший Создатель Карт Yu-Gi-Oh! - Бесплатный Онлайн Создатель Карт',
        vi: 'Trình Tạo Thẻ Yu-Gi-Oh! Tốt Nhất - Trình Tạo Thẻ Trực Tuyến Miễn Phí'
      }

      // Generate page description based on locale
      const descriptions = {
        en: 'Create custom Yu-Gi-Oh! cards with our free online card maker. Support for all card types including monsters, spells, traps, pendulum, and link monsters.',
        zh: '使用我們的免費在線卡片製造機創建自定義遊戲王卡片。支援所有卡片類型，包括怪獸、魔法、陷阱、靈擺和連結怪獸。',
        ja: '無料のオンラインカードメーカーでカスタム遊戯王カードを作成。モンスター、魔法、罠、ペンデュラム、リンクモンスターなど全てのカードタイプに対応。',
        de: 'Erstellen Sie benutzerdefinierte Yu-Gi-Oh! Karten mit unserem kostenlosen Online-Kartenmacher. Unterstützung für alle Kartentypen einschließlich Monster, Zauber, Fallen, Pendel und Link-Monster.',
        fr: 'Créez des cartes Yu-Gi-Oh! personnalisées avec notre créateur de cartes en ligne gratuit. Support pour tous les types de cartes y compris les monstres, sorts, pièges, pendule et monstres lien.',
        ko: '무료 온라인 카드 제작기로 맞춤형 유희왕 카드를 만드세요. 몬스터, 마법, 함정, 펜듈럼, 링크 몬스터를 포함한 모든 카드 유형을 지원합니다.',
        pt: 'Crie cartas Yu-Gi-Oh! personalizadas com nosso criador de cartas online gratuito. Suporte para todos os tipos de cartas incluindo monstros, magias, armadilhas, pêndulo e monstros link.',
        es: 'Crea cartas Yu-Gi-Oh! personalizadas con nuestro creador de cartas en línea gratuito. Soporte para todos los tipos de cartas incluyendo monstruos, hechizos, trampas, péndulo y monstruos enlace.',
        el: 'Δημιουργήστε προσαρμοσμένες κάρτες Yu-Gi-Oh! με τον δωρεάν online δημιουργό καρτών μας. Υποστήριξη για όλους τους τύπους καρτών συμπεριλαμβανομένων τεράτων, ξόρκια, παγίδες, εκκρεμές και link τέρατα.',
        th: 'สร้างการ์ด Yu-Gi-Oh! แบบกำหนดเองด้วยเครื่องมือสร้างการ์ดออนไลน์ฟรีของเรา รองรับการ์ดทุกประเภทรวมถึงมอนสเตอร์ เวทมนตร์ กับดัก เพนดูลัม และลิงค์มอนสเตอร์',
        ru: 'Создавайте пользовательские карты Yu-Gi-Oh! с помощью нашего бесплатного онлайн-создателя карт. Поддержка всех типов карт, включая монстров, заклинания, ловушки, маятник и линк-монстров.',
        vi: 'Tạo thẻ Yu-Gi-Oh! tùy chỉnh với trình tạo thẻ trực tuyến miễn phí của chúng tôi. Hỗ trợ tất cả các loại thẻ bao gồm quái vật, phép thuật, bẫy, pendulum và link monster.'
      }

      // Generate canonical URL
      const canonicalUrl = currentLocale === 'en'
        ? (cleanPath === '/' ? `${baseUrl}/` : `${baseUrl}${cleanPath}`)
        : (cleanPath === '/' ? `${baseUrl}/${currentLocale}/` : `${baseUrl}/${currentLocale}${cleanPath}`)

      // Generate hreflang links
      let hreflangLinks = ''
      languages.forEach(lang => {
        const href = lang === 'en'
          ? (cleanPath === '/' ? `${baseUrl}/` : `${baseUrl}${cleanPath}`)
          : (cleanPath === '/' ? `${baseUrl}/${lang}/` : `${baseUrl}/${lang}${cleanPath}`)
        hreflangLinks += `<link rel="alternate" hreflang="${lang}" href="${href}">`
      })

      // Add x-default hreflang
      const xDefaultHref = cleanPath === '/' ? `${baseUrl}/` : `${baseUrl}${cleanPath}`
      hreflangLinks += `<link rel="alternate" hreflang="x-default" href="${xDefaultHref}">`

      // Add canonical link
      const canonicalLink = `<link rel="canonical" href="${canonicalUrl}">`

      // Get page title and description
      const pageTitle = titles[currentLocale] || titles.en
      const pageDescription = descriptions[currentLocale] || descriptions.en

      // Add page-specific meta tags
      const metaTags = `
        <meta data-hid="description" name="description" content="${pageDescription}">
        <meta data-hid="og:title" property="og:title" content="${pageTitle}">
        <meta data-hid="og:description" property="og:description" content="${pageDescription}">
        <meta data-hid="og:url" property="og:url" content="${canonicalUrl}">
        <meta data-hid="twitter:title" name="twitter:title" content="${pageTitle}">
        <meta data-hid="twitter:description" name="twitter:description" content="${pageDescription}">
      `

      // Update HTML with correct lang attribute, title, and meta tags
      let updatedHtml = html
        // Update html lang attribute
        .replace(/<html[^>]*>/, `<html lang="${currentLocale}" data-n-head="%7B%22lang%22:%7B%221%22:%22${currentLocale}%22%7D%7D">`)
        // Update title
        .replace(/<title>Yu-Gi-Oh! Card Maker<\/title>/, `<title>${pageTitle}</title>`)
        // Add meta tags and links before closing head tag
        .replace('</head>', `${metaTags}${canonicalLink}${hreflangLinks}</head>`)

      page.html = updatedHtml
    }
  }
}
